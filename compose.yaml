services:

  redis-cluster:
    # image taken as 6.x supporting both x64 and arm64, as g<PERSON><PERSON> doesn't have native arm64
    image: tommy351/redis-cluster:6.2
    container_name: redis-cluster
    restart: unless-stopped
    ports:
      - "7000-7003:7000-7003"
    environment:
      IP: "0.0.0.0"
    networks:
      - aaal

  redisinsight:
    image: redislabs/redisinsight:latest
    hostname: redisinsight
    container_name: redisinsight
    restart: unless-stopped
    ports:
      - "5540:5540"
    networks:
      - aaal

  redis-init:
    image: redis:6.2-alpine
    container_name: redis-init
    depends_on:
      - redis-cluster
    command: >
      sh -c "
        sleep 10 &&
        redis-cli -h redis-cluster -p 7000 SET 'SESSIONS/abc123' '{\"created\": 1719916800, \"userId\": 12345, \"isManager\": true, \"csrfToken\": \"abc123csrf\", \"canSeeAllBrands\": false, \"azureADSessionId\": \"aad-session-67890\", \"isDev\": true, \"accessToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\", \"refreshToken\": \"def456refresh\"}' &&
        echo 'Redis data initialized successfully'
      "
    restart: "no"
    networks:
      - aaal

  lambda-authorizer:
    image: public.ecr.aws/lambda/provided:al2023
    hostname: lambda-authorizer
    container_name: lambda-authorizer
    platform: linux/amd64
    ports:
      - "9000:8080"
    volumes:
      - ./build/native/nativeCompile/native:/var/task/native
      - ./src/main/resources/bootstrap:/var/runtime/bootstrap
    environment:
      _HANDLER: com.xm.affiliates.lambda.Authorizer::handleRequest
      REDIS_SERVERS: redis-cluster:7000,redis-cluster:7001,redis-cluster:7002
    command: com.xm.affiliates.lambda.Authorizer::handleRequest
    depends_on:
      - redis-cluster
    networks:
      - aaal

networks:
  aaal: