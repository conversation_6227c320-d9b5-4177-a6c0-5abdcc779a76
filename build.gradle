plugins {
    id 'application'
    id 'maven-publish'
    id 'org.graalvm.buildtools.native'
}

repositories {
    maven {
        url = xmReleasesUrl
        credentials {
            username = project.properties.xmReleasesUsername
            password = project.properties.xmReleasesPassword
        }
    }
    mavenCentral()

}

application {
    mainClass.set('com.amazonaws.services.lambda.runtime.api.client.AWSLambda')
}

dependencies {
    implementation "com.amazonaws:aws-lambda-java-core:$awsLambdaJavaCoreVersion"
    implementation "com.amazonaws:aws-lambda-java-events:$awsLambdaJavaEventsVersion"
    implementation "com.amazonaws:aws-lambda-java-runtime-interface-client:$awsLambdaJavaRuntimeInterfaceClientVersion"
    implementation "com.amazonaws:aws-java-sdk-iam:$awsJavaSdkIamVersion"
    implementation "com.amazonaws:aws-java-sdk-ssm:$awsJavaSdkSsmVersion"

    implementation "redis.clients:jedis:$jedisVersion"

    compileOnly "org.projectlombok:lombok:$lombokVersion"
    annotationProcessor "org.projectlombok:lombok:$lombokVersion"


    testImplementation "org.junit.jupiter:junit-jupiter-api:$junitJupiterVersion"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:$junitJupiterVersion"
    testImplementation "org.mockito:mockito-junit-jupiter:$mockitoVersion"
}

graalvmNative {
    agent {
        enabled = true
        defaultMode = "standard"

        metadataCopy {
            inputTaskNames.add("test")  // Copy metadata from test execution
            inputTaskNames.add("run")   // Copy metadata from run task
            outputDirectories.add("src/main/resources/META-INF/native-image")
            mergeWithExisting = true    // Merge with your existing config
        }

        modes {
            standard {
                // Standard mode - captures most reflection usage
            }
            conditional {
                // Conditional mode - more selective capturing
                userCodeFilterPath = "user-code-filter.json"
            }
        }
    }

    binaries {
        main {
            imageName = "native"
            verbose = true
            fallback = false
            buildArgs.add('--verbose')
            buildArgs.add('-J-Xmx8g')
            buildArgs.add('-J-Xms4g')
            buildArgs.add('--native-image-info')
            buildArgs.add('--no-fallback')
            buildArgs.add('--enable-http')
            buildArgs.add('--enable-https')
        }
    }
}

test {
    useJUnitPlatform()
}

tasks.register('createDistributionZip', Zip) {
    mustRunAfter(tasks.named('nativeCompile')) // Ensure 'nativeCompile' runs before this task logically

    destinationDirectory = file("$buildDir/distributions")
    from('src/main/resources/bootstrap') {
        into('.')
    }
    from('build/native/nativeCompile/native') {
        into('.')
    }
}
